defmodule Test.DoctestCase do
  use ExUnit.CaseTemplate

  defmacro defrelation(name) do
    table_name = Atom.to_string(name)
    module_name = Module.concat([MyApp, Drops.Inflector.camelize(name)])

    {:module, module, _, _} =
      Module.create(
        module_name,
        quote do
          use Drops.Relation, otp_app: :my_app
          schema(unquote(table_name), infer: true)
        end,
        Macro.Env.location(__ENV__)
      )

    quote do
      require unquote(module)
    end
  end

  using do
    quote do
      import unquote(__MODULE__)

      setup_all do
        Test.Repos.start_owner!(MyApp.Repo)

        on_exit(fn ->
          Test.Repos.stop_owner(MyApp.Repo)
        end)
      end

      setup tags do
        modules_before = Test.loaded_modules()

        if tags[:test_type] == :doctest do
          fixtures = tags[:fixtures] || []

          Test.Fixtures.load(fixtures)
        end

        on_exit(fn ->
          new_modules = MapSet.difference(Test.loaded_modules(), modules_before)
          test_module_prefix = to_string(__MODULE__)

          Enum.each(new_modules, fn module ->
            module_string = to_string(module)

            if String.starts_with?(module_string, test_module_prefix) do
              Test.clear_module(module_string)
            end

            if function_exported?(module, :schema, 0) do
              Test.cleanup_relation_modules(module)
            end
          end)
        end)

        :ok
      end
    end
  end
end
